import { useRouter } from 'next/router'
import { useAuth } from '@/contexts/AuthContext'
import { useEffect, useState } from 'react'
import { toast } from 'react-toastify'

export default function ProtectedRoute({
  children,
  adminOnly = false,
  devOnly = false,
  staffOnly = false,
  requiredRole = null
}) {
  const {
    user,
    role,
    loading: authLoading,
    hasAdminAccess,
    hasStaffAccess,
    hasFullAccess,
    isDev,
    isAdmin,
    isStaff
  } = useAuth()
  const router = useRouter()
  const [isAuthorized, setIsAuthorized] = useState(false)
  const [error, setError] = useState(null)
  const [loading, setLoading] = useState(true)

  // Handle authentication and authorization with enhanced redirect loop prevention
  useEffect(() => {
    // Skip if still loading auth state
    if (authLoading) {
      console.log('ProtectedRoute: Auth still loading...')
      return
    }

    const checkAccess = async () => {
      try {
        console.log('ProtectedRoute: Checking access for user:', user?.email, 'role:', role)

        // Check if we're already on a login page to prevent redirect loops
        const isOnLoginPage = router.pathname === '/admin/login' ||
                             router.pathname === '/admin/reset-password' ||
                             router.pathname === '/admin/forgot-password'

        // If no user and not on login page, redirect to login
        if (!user && !isOnLoginPage) {
          console.log('ProtectedRoute: No user found, redirecting to login')

          // Prevent multiple simultaneous redirects using component state
          if (router.events) {
            const handleRouteChangeStart = () => {
              sessionStorage.removeItem('protected_route_redirecting')
            }
            router.events.on('routeChangeStart', handleRouteChangeStart)
            return () => router.events.off('routeChangeStart', handleRouteChangeStart)
          }
          sessionStorage.setItem('redirect_after_login', router.asPath)

          // Use replace to prevent back button issues
          router.replace('/admin/login').finally(() => {
            setTimeout(() => {
              sessionStorage.removeItem('protected_route_redirecting')
            }, 1000)
          })
          return
        }

        // If user exists but we're on login page, redirect to intended destination
        if (user && isOnLoginPage) {
          const redirectPath = sessionStorage.getItem('redirect_after_login') || '/admin'
          sessionStorage.removeItem('redirect_after_login')
          console.log('ProtectedRoute: User authenticated on login page, redirecting to:', redirectPath)
          router.replace(redirectPath)
          return
        }

        // Enhanced authorization logic with new role system
        // Special case for known admin users (fallback)
        const knownAdmins = ['<EMAIL>', '<EMAIL>', '<EMAIL>']
        const isKnownAdmin = knownAdmins.includes(user.email);

        // Check specific role requirements
        let authorized = false
        let errorMessage = 'Access denied.'

        if (devOnly) {
          authorized = isDev || isKnownAdmin
          errorMessage = 'Access denied. Developer privileges required.'
        } else if (adminOnly) {
          authorized = hasAdminAccess || isKnownAdmin
          errorMessage = 'Access denied. Admin privileges required.'
        } else if (staffOnly) {
          authorized = hasStaffAccess || isKnownAdmin
          errorMessage = 'Access denied. Staff privileges required.'
        } else if (requiredRole) {
          authorized = role === requiredRole || isKnownAdmin
          errorMessage = `Access denied. ${requiredRole} role required.`
        } else {
          // Default: require at least staff access for admin panel
          authorized = hasStaffAccess || isKnownAdmin
          errorMessage = 'Access denied. Staff privileges required.'
        }

        if (!authorized) {
          console.log('ProtectedRoute: User lacks required privileges', {
            role,
            devOnly,
            adminOnly,
            staffOnly,
            requiredRole
          })
          setError(errorMessage)
          toast.error(errorMessage, {
            autoClose: 5000,
            position: 'top-center'
          })
          setIsAuthorized(false)
        } else {
          // User is authorized
          console.log('ProtectedRoute: User authorized', { role, authorized })
          setIsAuthorized(true)
          setError(null)
        }

        setLoading(false)
      } catch (error) {
        console.error('Access verification failed:', error)
        setError('Authentication failed: ' + (error?.message || 'Unknown error'))
        setLoading(false)
      }
    }

    checkAccess()
  }, [user, role, authLoading, adminOnly, devOnly, staffOnly, requiredRole, router, hasAdminAccess, hasStaffAccess, isDev])

  // Show loading state, error, or children
  if (authLoading || loading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen" data-testid="auth-loading">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mb-4"></div>
        <div className="text-gray-600 auth-loading">Authenticating...</div>
        <div className="text-xs text-gray-400 mt-2">
          If this takes too long, the page will automatically recover
        </div>
      </div>
    )
  }

  if (!isAuthorized) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <div className="text-red-500 text-center mt-4 px-4 max-w-md">
          <p className="font-semibold">Authorization Error:</p>
          <p>{error || 'Unauthorized access'}</p>
          <button
            onClick={() => router.push('/admin/login')}
            className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
          >
            Return to Login
          </button>
        </div>
      </div>
    )
  }

  return children
}
